import 'package:equatable/equatable.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';

part 'derivative_condition_order_edit_state.dart';

class DerivativeConditionOrderEditCubit
    extends Cubit<DerivativeConditionOrderEditState> {
  DerivativeConditionOrderEditCubit()
    : super(const DerivativeConditionOrderEditState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  void resetState() {
    emit(
      state.copyWith(
        status: DerivativeConditionOrderEditStatus.initial,
        errorMessage: null,
      ),
    );
  }

  Future<void> editDerivativeConditionOrder({
    required ConditionOrderRequestModel request,
  }) async {
    try {
      if (isClosed) return;

      emit(
        state.copyWith(
          status: DerivativeConditionOrderEditStatus.loading,
          errorMessage: null,
        ),
      );

      await _commandHistoryRepository.editFUConditonOrder(request);
      emit(state.copyWith(status: DerivativeConditionOrderEditStatus.success));
    } catch (error) {
      if (isClosed) return;
      emit(
        state.copyWith(
          status: DerivativeConditionOrderEditStatus.failure,
          errorMessage: await getErrorMessage(error),
        ),
      );
    }
  }
}
